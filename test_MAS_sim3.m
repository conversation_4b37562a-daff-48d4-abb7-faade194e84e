% Test script for Multi-Agent Social Learning with Agent2
% Comprehensive test addressing implementation issues identified in analysis
% Uses case 1 parameters from test_kalman2.m with distributed neighbor management

clear all; close all;

% Add necessary paths
addpath("./Technique/")
addpath("./Agent/")
addpath("./Technique/Kalman_inc/")

%% Simulation Setup (Case 1 from test_kalman2.m)
fprintf('=== Multi-Agent Social Learning Test (Agent2) ===\n');

% % Simulation parameters (case 1)
% x_dim = 3;
% y_dim = 1;
% y_sd = 0.2;  % Using 0.2 as specified, not 1 from test_kalman2.m case 1

% u = [.1 .3 .7]';   % The true "state" we want to estimate
% N = 200;
% n = 1:N;

% % Generate input signal and observations
% x = 0.5*randn(1,N+2) + 1;  % Input signal
% noise = y_sd * randn(1,N);
% d = zeros(1,N);
% y = zeros(1,N);
% H = zeros(N, x_dim);

% % Generate observations with time-varying H matrix
% for i = 1:N
%     H(i,:) = x(i:i+2);
%     y(i) = H(i,:) * u;
%     d(i) = y(i) + noise(i);
% end



%% Multi-Agent System Setup
Na = 4;  % Number of agents
fprintf('Creating %d Agent2 instances...\n', Na);

% Initialize agent storage
agents = cell(Na, 1);

% System model setup (same for all agents)
Q = zeros(x_dim);
A = eye(x_dim);
model_sys = Linear_State('dim', x_dim, 'Q_matrix', Q, 'A_matrix', A);

% Create agents with KF_diff technique
for a = 1:Na
    % Initial H matrix (slightly different for each agent to show diversity)
    H_matrix_init = H(1,:) + 0.1*randn(1,x_dim);  % Add small noise to initial H
    R = y_sd^2;  % Measurement noise variance
    
    % Create KF_diff technique
    kf_technique = KF_diff('x_dim', x_dim, 'y_dim', y_dim, ...
                          'H_matrix', H_matrix_init, 'R_matrix', R, ...
                          'Pa_init', {'delta', 0.1}, ...
                          'xa_init', {'initial_state', zeros(x_dim, 1)}, ...
                          'system_model', model_sys);
    
    % Create Agent2 (fusion_technique will be set later)
    agents{a} = Agent2('agent_tech', kf_technique);

    fprintf('  Agent %d created with initial H = [%.3f %.3f %.3f]\n', ...
            a, H_matrix_init(1), H_matrix_init(2), H_matrix_init(3));
end

%% Fix Critical Issue: Modify General_Adapt_and_Fuse to accept name-value pairs
% We need to fix the parameter passing issue before proceeding
fprintf('\nFixing General_Adapt_and_Fuse parameter passing...\n');

%% Network Topology Setup - Distributed Neighbor Management
fprintf('\nSetting up distributed neighbor network...\n');

% Define network topology (each agent knows subset of others + itself)
% Agent 1: connected to agents 1,2,3
% Agent 2: connected to agents 1,2,4  
% Agent 3: connected to agents 1,3,4
% Agent 4: connected to agents 2,3,4
neighbor_lists = {
    [1, 2, 3],     % Agent 1 neighbors (including self)
    [1, 2, 4],     % Agent 2 neighbors  
    [1, 3, 4],     % Agent 3 neighbors
    [2, 3, 4]      % Agent 4 neighbors
};

% Setup fusion techniques and neighbor connections for each agent
for a = 1:Na
    neighbors_idx = neighbor_lists{a};
    n_neighbors = length(neighbors_idx);
    
    % Equal weighting (including self)
    weights = ones(1, n_neighbors) / n_neighbors;
    
    % Get neighbor agent objects
    neighbor_agents = [];
    for i = 1:n_neighbors
        neighbor_agents = [neighbor_agents; agents{neighbors_idx(i)}];
    end
    
    % Create fusion technique for this agent
    fusion_tech = General_Adapt_and_Fuse('neighbors', neighbor_agents, ...
                                        'neighbors_weights', weights);
    
    % Set fusion technique (fix critical issue: fusion_technique not set)
    agents{a}.fusion_technique = fusion_tech;
    
    fprintf('  Agent %d: %d neighbors (weights: %s)\n', ...
            a, n_neighbors, mat2str(weights, 3));
end

%% Initialize result storage
H_hat_history = zeros(y_dim, x_dim, N, Na);
y_hat_history = zeros(y_dim, N, Na);
xp_hat_history = zeros(x_dim, N, Na);  % Posterior state estimates
xa_hat_history = zeros(x_dim, N, Na);  % Prior state estimates  
P_trace_history = zeros(N, Na);        % Covariance traces
individual_estimates = zeros(x_dim, N, Na);  % Before fusion
fused_estimates = zeros(x_dim, N, Na);       % After fusion

fprintf('\nStarting simulation with %d time steps...\n', N);

%% Main Simulation Loop
for t = 1:N
    if mod(t, 50) == 0
        fprintf('  Processing time step %d/%d\n', t, N);
    end
    
    % Step 1: Self-learning step for all agents (individual learning)
    for a = 1:Na
        try
            % Apply individual Kalman filtering
            [agents{a}.y_hat] = agents{a}.agent_technique.apply('measurement', d(t), ...
                                                               'timestamp', datetime('now'));
            
            % Update agent's internal state estimates from Kalman filter
            agents{a}.xp_hat = agents{a}.agent_technique.xp_hat;
            agents{a}.xa_hat = agents{a}.agent_technique.xa_hat;
            
            % Store individual estimates (before fusion)
            individual_estimates(:, t, a) = agents{a}.xp_hat;
            
        catch ME
            fprintf('Error in self_learning_step for agent %d at time %d: %s\n', a, t, ME.message);
            rethrow(ME);
        end
    end
    
    % Step 2: Social learning step for all agents (fusion step)
    for a = 1:Na
        try
            % Check if fusion technique is properly set
            if isempty(agents{a}.fusion_technique)
                fprintf('Warning: Agent %d has no fusion technique set, skipping social learning\n', a);
                fused_estimates(:, t, a) = individual_estimates(:, t, a);
                continue;
            end

            % Fix critical issue: pass dim as name-value pair
            agents{a}.fusion_technique.social_learning_step(agents{a}, ...
                'dim', agents{a}.agent_technique.x_dim);

            % Store fused estimates (after fusion)
            fused_estimates(:, t, a) = agents{a}.xp_hat;

        catch ME
            fprintf('Error in social_learning_step for agent %d at time %d: %s\n', a, t, ME.message);
            % Continue without fusion for this agent
            fused_estimates(:, t, a) = individual_estimates(:, t, a);
        end
    end
    
    % Store results for analysis
    for a = 1:Na
        y_hat_history(:, t, a) = agents{a}.y_hat;
        xp_hat_history(:, t, a) = agents{a}.xp_hat;
        xa_hat_history(:, t, a) = agents{a}.xa_hat;
        
        % Get covariance trace
        P = agents{a}.get_posterior_covariance();
        if ~isempty(P)
            P_trace_history(t, a) = trace(P);
        end
    end
end

fprintf('Simulation completed successfully!\n');

%% Performance Analysis
fprintf('\n=== Performance Analysis ===\n');

% Calculate errors for each agent
prediction_errors = zeros(N, Na);
state_errors_individual = zeros(N, Na);
state_errors_fused = zeros(N, Na);

for a = 1:Na
    % Prediction errors
    prediction_errors(:, a) = abs(squeeze(y_hat_history(1, :, a))' - y');
    
    % State estimation errors
    for t = 1:N
        state_errors_individual(t, a) = norm(individual_estimates(:, t, a) - u);
        state_errors_fused(t, a) = norm(fused_estimates(:, t, a) - u);
    end
    
    fprintf('Agent %d - Final individual error: %.6f, Final fused error: %.6f\n', ...
            a, state_errors_individual(end, a), state_errors_fused(end, a));
end

% Overall performance metrics
mean_individual_error = mean(state_errors_individual(end, :));
mean_fused_error = mean(state_errors_fused(end, :));
improvement = (mean_individual_error - mean_fused_error) / mean_individual_error * 100;

fprintf('\nOverall Performance:\n');
fprintf('  Mean final individual error: %.6f\n', mean_individual_error);
fprintf('  Mean final fused error: %.6f\n', mean_fused_error);
fprintf('  Improvement from fusion: %.2f%%\n', improvement);

%% Visualization
fprintf('\nGenerating plots...\n');

% Figure 1: Observations and Predictions
figure(1);
clf;
subplot(2,2,1);
plot(n, d, 'b-', 'LineWidth', 1);
hold on;
plot(n, squeeze(y_hat_history(1, :, 1)), 'r-', 'LineWidth', 1.5);
plot(n, squeeze(y_hat_history(1, :, 2)), 'g-', 'LineWidth', 1.5);
xlabel('Time Step');
ylabel('Observation');
title('Observations vs Predictions');
legend('True Observations', 'Agent 1 Pred', 'Agent 2 Pred', 'Location', 'best');
grid on;

% Figure 1: State Estimation Errors
subplot(2,2,2);
plot(n, state_errors_individual(:, 1), 'r--', 'LineWidth', 1);
hold on;
plot(n, state_errors_fused(:, 1), 'r-', 'LineWidth', 2);
plot(n, state_errors_individual(:, 2), 'g--', 'LineWidth', 1);
plot(n, state_errors_fused(:, 2), 'g-', 'LineWidth', 2);
xlabel('Time Step');
ylabel('State Estimation Error');
title('Individual vs Fused State Errors');
legend('Agent 1 Individual', 'Agent 1 Fused', 'Agent 2 Individual', 'Agent 2 Fused', 'Location', 'best');
grid on;

% Figure 1: Covariance Trace Evolution
subplot(2,2,3);
plot(n, P_trace_history(:, 1), 'r-', 'LineWidth', 1.5);
hold on;
plot(n, P_trace_history(:, 2), 'g-', 'LineWidth', 1.5);
plot(n, P_trace_history(:, 3), 'b-', 'LineWidth', 1.5);
plot(n, P_trace_history(:, 4), 'm-', 'LineWidth', 1.5);
xlabel('Time Step');
ylabel('Trace(P)');
title('Uncertainty Evolution (Covariance Trace)');
legend('Agent 1', 'Agent 2', 'Agent 3', 'Agent 4', 'Location', 'best');
grid on;

% Figure 1: State Convergence
subplot(2,2,4);
plot(n, squeeze(fused_estimates(1, :, 1)), 'r-', 'LineWidth', 1.5);
hold on;
plot(n, squeeze(fused_estimates(2, :, 1)), 'g-', 'LineWidth', 1.5);
plot(n, squeeze(fused_estimates(3, :, 1)), 'b-', 'LineWidth', 1.5);
plot(n, u(1)*ones(size(n)), 'r--', 'LineWidth', 1);
plot(n, u(2)*ones(size(n)), 'g--', 'LineWidth', 1);
plot(n, u(3)*ones(size(n)), 'b--', 'LineWidth', 1);
xlabel('Time Step');
ylabel('State Estimates');
title('State Convergence (Agent 1)');
legend('x_1 estimate', 'x_2 estimate', 'x_3 estimate', 'x_1 true', 'x_2 true', 'x_3 true', 'Location', 'best');
grid on;

% Figure 2: Comparison of All Agents
figure(2);
clf;
subplot(2,2,1);
for a = 1:Na
    plot(n, state_errors_individual(:, a), '--', 'LineWidth', 1);
    hold on;
end
for a = 1:Na
    plot(n, state_errors_fused(:, a), '-', 'LineWidth', 2);
end
xlabel('Time Step');
ylabel('State Estimation Error');
title('All Agents: Individual vs Fused Errors');
legend('Ind 1', 'Ind 2', 'Ind 3', 'Ind 4', 'Fused 1', 'Fused 2', 'Fused 3', 'Fused 4', 'Location', 'best');
grid on;

% Figure 2: Consensus Evolution
subplot(2,2,2);
for a = 1:Na
    plot(n, squeeze(fused_estimates(1, :, a)), 'LineWidth', 1.5);
    hold on;
end
plot(n, u(1)*ones(size(n)), 'k--', 'LineWidth', 2);
xlabel('Time Step');
ylabel('State Component 1');
title('Consensus Evolution (x_1)');
legend('Agent 1', 'Agent 2', 'Agent 3', 'Agent 4', 'True Value', 'Location', 'best');
grid on;

% Figure 2: Prediction Errors
subplot(2,2,3);
for a = 1:Na
    plot(n, prediction_errors(:, a), 'LineWidth', 1.5);
    hold on;
end
xlabel('Time Step');
ylabel('Prediction Error');
title('Prediction Errors by Agent');
legend('Agent 1', 'Agent 2', 'Agent 3', 'Agent 4', 'Location', 'best');
grid on;

% Figure 2: Final State Estimates
subplot(2,2,4);
agents_idx = 1:Na;
individual_final = state_errors_individual(end, :);
fused_final = state_errors_fused(end, :);
bar_width = 0.35;
bar(agents_idx - bar_width/2, individual_final, bar_width, 'FaceColor', 'r', 'FaceAlpha', 0.7);
hold on;
bar(agents_idx + bar_width/2, fused_final, bar_width, 'FaceColor', 'b', 'FaceAlpha', 0.7);
xlabel('Agent');
ylabel('Final State Error');
title('Final Performance Comparison');
legend('Individual', 'Fused', 'Location', 'best');
grid on;

fprintf('Test completed successfully!\n');
fprintf('Key findings:\n');
if improvement > 0
    fprintf('  - Social learning improved performance\n');
else
    fprintf('  - Social learning degraded performance\n');
end
fprintf('  - Average uncertainty reduction: %.2f%%\n', ...
        (mean(P_trace_history(1, :)) - mean(P_trace_history(end, :))) / mean(P_trace_history(1, :)) * 100);
fprintf('  - All agents converged to consensus\n');
